import { NgTemplateOutlet, UpperCasePipe } from '@angular/common';
import { Component, computed, inject, OnInit, signal } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { Router } from '@angular/router';
import { AuthService } from '@core/services/http/auth.service';
import { NotificationService } from '@core/services/notification.service';
import { DestroyService } from '@core/services/utils/destroy';

import {
  ILanguage,
  languageCodeType,
  LanguageService,
} from '@core/services/utils/language';
import { ThemeService } from '@core/services/utils/theme';
import { log } from '@core/utils/logger';
import { environment } from '@env/environment';
import { themeType } from '@models/enums/theme';
import { ISection } from '@models/interfaces/menu';
import { TranslateModule } from '@ngx-translate/core';
import { SimpleButtonComponent } from '@shared/buttons/simple-button/simple-button.component';
import { NzAvatarComponent } from 'ng-zorro-antd/avatar';
import { NzBadgeComponent } from 'ng-zorro-antd/badge';
import { NzButtonComponent } from 'ng-zorro-antd/button';
import { NzDividerComponent } from 'ng-zorro-antd/divider';
import { NzDrawerModule } from 'ng-zorro-antd/drawer';
import {
  NzDropDownDirective,
  NzDropdownMenuComponent,
} from 'ng-zorro-antd/dropdown';
import { NzEmptyComponent } from 'ng-zorro-antd/empty';
import { NzGridModule } from 'ng-zorro-antd/grid';
import { NzIconDirective } from 'ng-zorro-antd/icon';
import { NzListEmptyComponent, NzListModule } from 'ng-zorro-antd/list';
import {
  NzMenuDirective,
  NzMenuItemComponent,
  NzMenuModule,
} from 'ng-zorro-antd/menu';
import { NzSwitchComponent } from 'ng-zorro-antd/switch';
import { NzTooltipDirective } from 'ng-zorro-antd/tooltip';
import { takeUntil } from 'rxjs';

@Component({
  selector: 'app-top-bar',
  standalone: true,
  imports: [
    NzDropDownDirective,
    NzDropdownMenuComponent,
    TranslateModule,
    NzAvatarComponent,
    NzBadgeComponent,
    NzButtonComponent,
    NzMenuItemComponent,
    NzMenuDirective,
    NzListEmptyComponent,
    NzSwitchComponent,
    FormsModule,
    TranslateModule,
    NzEmptyComponent,
    NzIconDirective,
    NzMenuModule,
    NzGridModule,
    NzDividerComponent,
    NzDrawerModule,
    NzTooltipDirective,
    NgTemplateOutlet,
    NzListModule,
    SimpleButtonComponent,
    UpperCasePipe,
  ],
  providers: [DestroyService],
  templateUrl: './top-bar.component.html',
  styleUrl: './top-bar.component.less',
})
export class TopBarComponent implements OnInit {
  // SERVICES
  private themeService = inject(ThemeService);
  private languageService = inject(LanguageService);
  private authService = inject(AuthService);
  private notificationService = inject(NotificationService);
  private router = inject(Router);
  private destroy$ = inject(DestroyService);

  // VARIABLES
  public currentLanguage: languageCodeType;
  public currentTheme: themeType;
  public languages: ILanguage[] = environment.languages;
  public themeType = themeType;
  readonly userAvatarName = computed(
    () =>
      this.authService.user()?.name?.slice(0, 1)! +
      this.authService.user()?.surname?.slice(0, 1),
  );

  isOwner = computed(() => this.authService.user()?.owner);

  protected notifications = this.notificationService.notifications;
  protected menus: ISection[] = environment.sections;
  protected mobileDrawerVisible = signal<boolean>(false);
  protected isCollapsed = false;

  protected isSoundEnabled = signal<boolean>(false);

  constructor() {
    this.currentTheme = this.themeService.currentTheme;
    this.currentLanguage = this.languageService.currentLanguage!.code;
  }

  ngOnInit(): void {
    this.isSoundEnabled.set(
      localStorage.getItem('notificationSoundConsent') === 'true',
    );

    this.themeService.theme$
      .pipe(takeUntil(this.destroy$))
      .subscribe((theme) => {
        this.currentTheme = theme;
      });

    this.languageService.language$
      .pipe(takeUntil(this.destroy$))
      .subscribe((lang) => (this.currentLanguage = lang));
  }

  changeLanguage(language: ILanguage) {
    log('LANGUAGE SELECTED: ', language);
    this.languageService.setLanguage(language);
  }

  onMenuClick(menu: ISection) {
    this.router.navigate([menu.routerLink]);
    this.mobileDrawerVisible.set(false);
  }

  onLogoutClick() {
    this.authService.logout().subscribe();
  }

  changeTheme(theme: themeType) {
    this.themeService.toggleTheme(theme);
  }

  onRemoveNotification(id: string) {
    this.notificationService.onRemoveNotification(id);
  }

  clearNotifications() {
    this.notificationService.clearNotifications();
  }

  onMobileDrawerOpen() {
    this.mobileDrawerVisible.set(true);
  }

  onMobileDrawerClose() {
    this.mobileDrawerVisible.set(false);
  }

  onProfileClick() {
    this.router.navigate(['/profile/info']);
  }

  changeSoundPreference() {
    this.isSoundEnabled.set(!this.isSoundEnabled());
    localStorage.setItem(
      'notificationSoundConsent',
      this.isSoundEnabled().toString(),
    );
  }
}
